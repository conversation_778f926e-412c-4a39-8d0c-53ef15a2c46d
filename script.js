class DragonChessGame {
    constructor() {
        this.board = [];
        this.boardSize = 8;
        this.gameBoard = document.getElementById('gameBoard');
        this.gameStatus = document.getElementById('gameStatus');
        this.draggedPiece = null;
        this.draggedFrom = null;
        
        // Diagonal shift pattern - each row shifts left by 1 position
        // Base pattern: Orange, Blue, Magenta, Pink, Yellow, Green, Red, Brown
        const baseColors = ['orange', 'blue', 'magenta', 'pink', 'yellow', 'green', 'red', 'brown'];
        this.boardPattern = [];

        // Generate 8 rows with diagonal shift pattern
        for (let row = 0; row < 8; row++) {
            const rowColors = [];
            for (let col = 0; col < 8; col++) {
                // Shift left by row number, wrap around using modulo
                const colorIndex = (col + row) % 8;
                rowColors.push(baseColors[colorIndex]);
            }
            this.boardPattern.push(rowColors);
        }

        this.colorClasses = {
            'red': 'color-red',
            'blue': 'color-blue',
            'green': 'color-green',
            'yellow': 'color-yellow',
            'purple': 'color-purple',
            'orange': 'color-orange',
            'pink': 'color-pink',
            'brown': 'color-brown'
        };

        // Each piece type has its own color and starts on matching colored squares
        this.pieceTypes = [
            { type: 'piece-red', symbol: '●', name: 'Red', color: 'red' },
            { type: 'piece-blue', symbol: '●', name: 'Blue', color: 'blue' },
            { type: 'piece-green', symbol: '●', name: 'Green', color: 'green' },
            { type: 'piece-yellow', symbol: '●', name: 'Yellow', color: 'yellow' },
            { type: 'piece-purple', symbol: '●', name: 'Purple', color: 'purple' },
            { type: 'piece-orange', symbol: '●', name: 'Orange', color: 'orange' },
            { type: 'piece-pink', symbol: '●', name: 'Pink', color: 'pink' },
            { type: 'piece-brown', symbol: '●', name: 'Brown', color: 'brown' }
        ];
        
        this.init();
    }
    
    init() {
        this.createBoard();
        this.setupEventListeners();
        this.updateStatus('Game ready! Each colored piece starts on its matching colored square. Drag to move!');
    }
    
    createBoard() {
        this.gameBoard.innerHTML = '';
        this.board = [];
        
        // Create 8x8 board
        for (let row = 0; row < this.boardSize; row++) {
            this.board[row] = [];
            for (let col = 0; col < this.boardSize; col++) {
                const square = this.createSquare(row, col);
                this.gameBoard.appendChild(square);
                this.board[row][col] = square;
            }
        }
        
        this.placePieces();
    }
    
    createSquare(row, col) {
        const square = document.createElement('div');
        square.className = 'board-square';
        square.dataset.row = row;
        square.dataset.col = col;

        // Use the exact color pattern from the board
        const squareColor = this.boardPattern[row][col];
        const colorClass = this.colorClasses[squareColor];
        square.classList.add(colorClass);
        square.dataset.color = squareColor;

        // Add drag and drop event listeners
        square.addEventListener('dragover', this.handleDragOver.bind(this));
        square.addEventListener('drop', this.handleDrop.bind(this));
        square.addEventListener('dragenter', this.handleDragEnter.bind(this));
        square.addEventListener('dragleave', this.handleDragLeave.bind(this));

        return square;
    }
    
    placePieces() {
        // Place pieces on their matching colored squares
        // Find squares for each color and place corresponding pieces
        const piecesPerColor = 2; // Number of pieces per color

        this.pieceTypes.forEach((pieceType, typeIndex) => {
            const matchingSquares = [];

            // Find all squares that match this piece's color
            for (let row = 0; row < this.boardSize; row++) {
                for (let col = 0; col < this.boardSize; col++) {
                    if (this.boardPattern[row][col] === pieceType.color) {
                        matchingSquares.push({ row, col });
                    }
                }
            }

            // Place pieces on some of the matching squares
            const squaresToUse = matchingSquares.slice(0, piecesPerColor);
            squaresToUse.forEach(({ row, col }) => {
                this.createPiece(row, col, typeIndex);
            });
        });
    }
    
    createPiece(row, col, typeIndex) {
        const piece = document.createElement('div');
        const pieceType = this.pieceTypes[typeIndex];
        
        piece.className = `game-piece ${pieceType.type}`;
        piece.textContent = pieceType.symbol;
        piece.draggable = true;
        piece.dataset.type = pieceType.name;
        piece.dataset.row = row;
        piece.dataset.col = col;
        
        // Add drag event listeners
        piece.addEventListener('dragstart', this.handleDragStart.bind(this));
        piece.addEventListener('dragend', this.handleDragEnd.bind(this));
        
        this.board[row][col].appendChild(piece);
        return piece;
    }
    
    handleDragStart(e) {
        this.draggedPiece = e.target;
        this.draggedFrom = {
            row: parseInt(e.target.dataset.row),
            col: parseInt(e.target.dataset.col)
        };
        
        e.target.classList.add('dragging');
        this.updateStatus(`Moving ${e.target.dataset.type}...`);
        
        // Highlight possible drop targets (all squares)
        this.highlightDropTargets();
    }
    
    handleDragEnd(e) {
        e.target.classList.remove('dragging');
        this.clearHighlights();
        
        if (!this.draggedPiece.parentElement.classList.contains('board-square')) {
            // If piece wasn't dropped on a valid square, return it to original position
            this.board[this.draggedFrom.row][this.draggedFrom.col].appendChild(this.draggedPiece);
        }
        
        this.draggedPiece = null;
        this.draggedFrom = null;
    }
    
    handleDragOver(e) {
        e.preventDefault();
    }
    
    handleDragEnter(e) {
        if (e.target.classList.contains('board-square')) {
            e.target.classList.add('drop-target');
        }
    }
    
    handleDragLeave(e) {
        if (e.target.classList.contains('board-square')) {
            e.target.classList.remove('drop-target');
        }
    }
    
    handleDrop(e) {
        e.preventDefault();
        
        if (!this.draggedPiece || !e.target.classList.contains('board-square')) {
            return;
        }
        
        const targetSquare = e.target;
        const targetRow = parseInt(targetSquare.dataset.row);
        const targetCol = parseInt(targetSquare.dataset.col);
        
        // Remove any existing piece from target square
        const existingPiece = targetSquare.querySelector('.game-piece');
        if (existingPiece) {
            existingPiece.remove();
        }
        
        // Move the piece to the new square
        targetSquare.appendChild(this.draggedPiece);
        
        // Update piece position data
        this.draggedPiece.dataset.row = targetRow;
        this.draggedPiece.dataset.col = targetCol;
        
        this.updateStatus(`${this.draggedPiece.dataset.type} moved to position (${targetRow + 1}, ${targetCol + 1})`);
        
        targetSquare.classList.remove('drop-target');
    }
    
    highlightDropTargets() {
        const squares = document.querySelectorAll('.board-square');
        squares.forEach(square => {
            square.classList.add('highlight');
        });
    }
    
    clearHighlights() {
        const squares = document.querySelectorAll('.board-square');
        squares.forEach(square => {
            square.classList.remove('highlight', 'drop-target');
        });
    }
    
    updateStatus(message) {
        this.gameStatus.textContent = message;
    }
    
    resetGame() {
        this.createBoard();
        this.updateStatus('Game reset! Pieces back on their matching colored squares.');
    }
    
    shufflePieces() {
        // Remove all pieces from board
        const pieces = document.querySelectorAll('.game-piece');
        pieces.forEach(piece => piece.remove());

        // Place pieces randomly on their matching colored squares
        this.placePieces();

        this.updateStatus('Pieces shuffled to new positions!');
    }
    
    setupEventListeners() {
        document.getElementById('resetBtn').addEventListener('click', () => {
            this.resetGame();
        });
        
        document.getElementById('randomizeBtn').addEventListener('click', () => {
            this.shufflePieces();
        });
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new DragonChessGame();
});
