class DragonChessGame {
    constructor() {
        this.board = [];
        this.boardSize = 8;
        this.gameBoard = document.getElementById('gameBoard');
        this.gameStatus = document.getElementById('gameStatus');
        this.draggedPiece = null;
        this.draggedFrom = null;
        
        this.colors = [
            'color-red', 'color-blue', 'color-green', 'color-yellow',
            'color-purple', 'color-orange', 'color-pink', 'color-cyan'
        ];
        
        this.pieceTypes = [
            { type: 'piece-dragon', symbol: '龍', name: 'Dragon' },
            { type: 'piece-tiger', symbol: '虎', name: 'Tiger' },
            { type: 'piece-phoenix', symbol: '鳳', name: 'Phoenix' },
            { type: 'piece-turtle', symbol: '龜', name: 'Turtle' },
            { type: 'piece-snake', symbol: '蛇', name: 'Snake' },
            { type: 'piece-crane', symbol: '鶴', name: 'Crane' }
        ];
        
        this.init();
    }
    
    init() {
        this.createBoard();
        this.setupEventListeners();
        this.updateStatus('Game ready! Drag any piece to move it anywhere on the board.');
    }
    
    createBoard() {
        this.gameBoard.innerHTML = '';
        this.board = [];
        
        // Create 8x8 board
        for (let row = 0; row < this.boardSize; row++) {
            this.board[row] = [];
            for (let col = 0; col < this.boardSize; col++) {
                const square = this.createSquare(row, col);
                this.gameBoard.appendChild(square);
                this.board[row][col] = square;
            }
        }
        
        this.placePieces();
    }
    
    createSquare(row, col) {
        const square = document.createElement('div');
        square.className = 'board-square';
        square.dataset.row = row;
        square.dataset.col = col;
        
        // Add random color to each square
        const randomColor = this.colors[Math.floor(Math.random() * this.colors.length)];
        square.classList.add(randomColor);
        
        // Add drag and drop event listeners
        square.addEventListener('dragover', this.handleDragOver.bind(this));
        square.addEventListener('drop', this.handleDrop.bind(this));
        square.addEventListener('dragenter', this.handleDragEnter.bind(this));
        square.addEventListener('dragleave', this.handleDragLeave.bind(this));
        
        return square;
    }
    
    placePieces() {
        // Place some initial pieces randomly
        const initialPieces = [
            { row: 0, col: 0, type: 0 },
            { row: 0, col: 7, type: 1 },
            { row: 7, col: 0, type: 2 },
            { row: 7, col: 7, type: 3 },
            { row: 3, col: 3, type: 4 },
            { row: 4, col: 4, type: 5 },
            { row: 1, col: 3, type: 0 },
            { row: 6, col: 4, type: 1 },
            { row: 2, col: 6, type: 2 },
            { row: 5, col: 1, type: 3 }
        ];
        
        initialPieces.forEach(({ row, col, type }) => {
            this.createPiece(row, col, type);
        });
    }
    
    createPiece(row, col, typeIndex) {
        const piece = document.createElement('div');
        const pieceType = this.pieceTypes[typeIndex];
        
        piece.className = `game-piece ${pieceType.type}`;
        piece.textContent = pieceType.symbol;
        piece.draggable = true;
        piece.dataset.type = pieceType.name;
        piece.dataset.row = row;
        piece.dataset.col = col;
        
        // Add drag event listeners
        piece.addEventListener('dragstart', this.handleDragStart.bind(this));
        piece.addEventListener('dragend', this.handleDragEnd.bind(this));
        
        this.board[row][col].appendChild(piece);
        return piece;
    }
    
    handleDragStart(e) {
        this.draggedPiece = e.target;
        this.draggedFrom = {
            row: parseInt(e.target.dataset.row),
            col: parseInt(e.target.dataset.col)
        };
        
        e.target.classList.add('dragging');
        this.updateStatus(`Moving ${e.target.dataset.type}...`);
        
        // Highlight possible drop targets (all squares)
        this.highlightDropTargets();
    }
    
    handleDragEnd(e) {
        e.target.classList.remove('dragging');
        this.clearHighlights();
        
        if (!this.draggedPiece.parentElement.classList.contains('board-square')) {
            // If piece wasn't dropped on a valid square, return it to original position
            this.board[this.draggedFrom.row][this.draggedFrom.col].appendChild(this.draggedPiece);
        }
        
        this.draggedPiece = null;
        this.draggedFrom = null;
    }
    
    handleDragOver(e) {
        e.preventDefault();
    }
    
    handleDragEnter(e) {
        if (e.target.classList.contains('board-square')) {
            e.target.classList.add('drop-target');
        }
    }
    
    handleDragLeave(e) {
        if (e.target.classList.contains('board-square')) {
            e.target.classList.remove('drop-target');
        }
    }
    
    handleDrop(e) {
        e.preventDefault();
        
        if (!this.draggedPiece || !e.target.classList.contains('board-square')) {
            return;
        }
        
        const targetSquare = e.target;
        const targetRow = parseInt(targetSquare.dataset.row);
        const targetCol = parseInt(targetSquare.dataset.col);
        
        // Remove any existing piece from target square
        const existingPiece = targetSquare.querySelector('.game-piece');
        if (existingPiece) {
            existingPiece.remove();
        }
        
        // Move the piece to the new square
        targetSquare.appendChild(this.draggedPiece);
        
        // Update piece position data
        this.draggedPiece.dataset.row = targetRow;
        this.draggedPiece.dataset.col = targetCol;
        
        this.updateStatus(`${this.draggedPiece.dataset.type} moved to position (${targetRow + 1}, ${targetCol + 1})`);
        
        targetSquare.classList.remove('drop-target');
    }
    
    highlightDropTargets() {
        const squares = document.querySelectorAll('.board-square');
        squares.forEach(square => {
            square.classList.add('highlight');
        });
    }
    
    clearHighlights() {
        const squares = document.querySelectorAll('.board-square');
        squares.forEach(square => {
            square.classList.remove('highlight', 'drop-target');
        });
    }
    
    updateStatus(message) {
        this.gameStatus.textContent = message;
    }
    
    resetGame() {
        this.createBoard();
        this.updateStatus('Game reset! Ready to play.');
    }
    
    randomizeColors() {
        const squares = document.querySelectorAll('.board-square');
        squares.forEach(square => {
            // Remove existing color classes
            this.colors.forEach(color => square.classList.remove(color));
            
            // Add new random color
            const randomColor = this.colors[Math.floor(Math.random() * this.colors.length)];
            square.classList.add(randomColor);
        });
        
        this.updateStatus('Board colors randomized!');
    }
    
    setupEventListeners() {
        document.getElementById('resetBtn').addEventListener('click', () => {
            this.resetGame();
        });
        
        document.getElementById('randomizeBtn').addEventListener('click', () => {
            this.randomizeColors();
        });
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new DragonChessGame();
});
