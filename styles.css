* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 800px;
    width: 100%;
}

h1 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-board-container {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

.game-board {
    display: grid;
    grid-template-columns: repeat(8, 60px);
    grid-template-rows: repeat(8, 60px);
    gap: 2px;
    background: #2c3e50;
    padding: 10px;
    border-radius: 15px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.board-square {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    border: 2px solid transparent;
}

.board-square:hover {
    transform: scale(1.05);
    border-color: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.board-square.highlight {
    border-color: #f39c12;
    box-shadow: 0 0 20px rgba(243, 156, 18, 0.6);
}

.board-square.drop-target {
    border-color: #27ae60;
    box-shadow: 0 0 20px rgba(39, 174, 96, 0.6);
}

/* Colorful square backgrounds matching the board pattern */
.color-red { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
.color-blue { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
.color-green { background: linear-gradient(135deg, #a8e6cf, #88d8a3); }
.color-yellow { background: linear-gradient(135deg, #ffd93d, #f39c12); }
.color-magenta { background: linear-gradient(135deg, #ff69b4, #e91e63); }
.color-orange { background: linear-gradient(135deg, #ffb347, #e67e22); }
.color-pink { background: linear-gradient(135deg, #ff8a95, #e91e63); }
.color-brown { background: linear-gradient(135deg, #8b4513, #a0522d); }
.color-purple { background: linear-gradient(135deg, #9b59b6, #8e44ad); }

.game-piece {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: grab;
    transition: all 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
    position: relative;
}

/* Black pieces - thick black frame with colored interior */
.black-piece {
    border: 5px solid #000;
    color: #000;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.9);
    box-shadow: 0 0 0 2px #000, 0 4px 8px rgba(0, 0, 0, 0.4);
}

/* White pieces - thick white frame with colored interior */
.white-piece {
    border: 5px solid #fff;
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.9);
    box-shadow: 0 0 0 2px #fff, 0 4px 8px rgba(0, 0, 0, 0.4);
}

.game-piece:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

.game-piece.dragging {
    cursor: grabbing;
    transform: scale(1.2);
    z-index: 1000;
    opacity: 0.8;
}

/* Piece colors matching their square colors */
.piece-red { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
.piece-blue { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
.piece-green { background: linear-gradient(135deg, #a8e6cf, #88d8a3); }
.piece-yellow { background: linear-gradient(135deg, #ffd93d, #f39c12); }
.piece-purple { background: linear-gradient(135deg, #a8a8ff, #8e44ad); }
.piece-orange { background: linear-gradient(135deg, #ffb347, #e67e22); }
.piece-pink { background: linear-gradient(135deg, #ff8a95, #e91e63); }
.piece-brown { background: linear-gradient(135deg, #8b4513, #a0522d); }

.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.player-info h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.player-info p {
    color: #7f8c8d;
    font-size: 1.1em;
}

.controls {
    display: flex;
    gap: 15px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.btn:active {
    transform: translateY(0);
}

@media (max-width: 768px) {
    .game-board {
        grid-template-columns: repeat(8, 45px);
        grid-template-rows: repeat(8, 45px);
    }
    
    .board-square {
        width: 45px;
        height: 45px;
    }
    
    .game-piece {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
    
    h1 {
        font-size: 2em;
    }
    
    .game-info {
        flex-direction: column;
        text-align: center;
    }
}
