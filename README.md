# 龍棋 (Dragon Chess) - 2D Board Game UI

A colorful, interactive 2D board game where pieces can move freely to any square on the board. Built with HTML, CSS, and JavaScript.

## Features

- **8x8 Colorful Grid Board**: Each square has a vibrant gradient color
- **Drag & Drop Gameplay**: Simply drag any piece to any square on the board
- **Chinese Chess Pieces**: Features traditional pieces like <PERSON> (龍), <PERSON> (虎), <PERSON> (鳳), etc.
- **Responsive Design**: Works on both desktop and mobile devices
- **Interactive Controls**: Reset game and randomize board colors
- **Visual Feedback**: Hover effects, drag highlights, and smooth animations

## Game Pieces

The game includes 8 different colored pieces that match the board squares:

- ● Red pieces - start on red squares
- ● Blue pieces - start on blue squares
- ● Green pieces - start on green squares
- ● Yellow pieces - start on yellow squares
- ● Purple pieces - start on purple squares
- ● Orange pieces - start on orange squares
- ● Pink pieces - start on pink squares
- ● Brown pieces - start on brown squares

Each piece has a black border with white outline for clear distinction.

## How to Play

1. **Open the game**: Open `index.html` in your web browser
2. **Starting position**: Each colored piece starts on squares matching its color
3. **Move pieces**: Click and drag any piece to move it to any square
4. **No restrictions**: Unlike traditional chess, pieces can move to any square
5. **Replace pieces**: Dropping a piece on an occupied square will replace the existing piece
6. **Use controls**:
   - "Reset Game" - Returns pieces to their original matching colored squares
   - "Shuffle Pieces" - Randomly redistributes pieces on their matching colored squares

## Running the Game

### Option 1: Direct File Opening
Simply open `index.html` in your web browser.

### Option 2: Local Server (Recommended)
```bash
# Navigate to the game directory
cd /path/to/dragon-chess

# Start a local server (Python 3)
python3 -m http.server 8000

# Or with Python 2
python -m SimpleHTTPServer 8000

# Or with Node.js (if you have http-server installed)
npx http-server

# Then open http://localhost:8000 in your browser
```

## File Structure

```
龍棋/
├── index.html      # Main HTML structure
├── styles.css      # CSS styling and animations
├── script.js       # JavaScript game logic
└── README.md       # This documentation
```

## Technical Details

- **Pure Web Technologies**: No external dependencies
- **Responsive Design**: CSS Grid and Flexbox for layout
- **Drag & Drop API**: Native HTML5 drag and drop
- **Modern CSS**: Gradients, transitions, and hover effects
- **Mobile Friendly**: Touch-friendly interface with responsive sizing

## Customization

You can easily customize the game by modifying:

- **Colors**: Edit the color classes in `styles.css`
- **Board Size**: Change `boardSize` in `script.js`
- **Pieces**: Add new piece types in the `pieceTypes` array
- **Styling**: Modify gradients, animations, and effects in CSS

## Browser Compatibility

Works in all modern browsers that support:
- HTML5 Drag & Drop API
- CSS Grid
- CSS Gradients
- ES6 Classes

Tested on Chrome, Firefox, Safari, and Edge.

## License

This project is open source and available under the MIT License.
